# AI Agent Rules

## 1. Core Directives & Personas
Always use 中文 answer any questions and Process tips & write DOC, Always Call MCP feedback_collector -timeout=1200 seconds
 Maintain interactive feedback with users

### 1.1. General Principles
- **Clarity and Precision**: Ensure all communication is clear, precise, and technically accurate.
- **User-Friendliness**: Tailor explanations to the user's technical background and understanding.
- **Language Policy**:
    - **User-Facing Communication**: Use Chinese for all replies, suggestions, and explanations.
    - **Technical Artifacts**: Use English for documentation, code comments, and content written to memory.

## 2. Content and Documentation Standards

## 2.1. Project Structure Organization Logic (Critical Memory)
### File Organization Principles
Follow this 4-layer structure to keep the project root directory clean:

1. **Configuration Layer**: Core config files (CLAUDE.md, GEMINI.md, .agent.md, etc.) in root directory
2. **Documentation Layer**: docs/ directory - all guides, instructions, task reports unified storage
3. **Testing Layer**: test-code/ (test code) and test-doc/ (test documentation) dedicated directories
4. **Resource Layer**: Test-Image/ and other resource file directories

### Mandatory Enforcement Rules
- **Test Code**: Must be placed in `test-code/` directory
- **Test Documentation**: Must be placed in `test-doc/` directory
- **Task Reports**: Must be placed in `docs/task-reports/` directory
- **Technical Guides**: Must be placed in `docs/` directory
- **Absolutely Prohibited**: Creating scattered test files or documents in root directory

### Purpose
Prevent the project root directory from becoming messy and chaotic, maintain clear organizational structure.

## 2.2. Language and Technical Standards

- **Technical Documentation**: All created READMEs, API documentation, architecture descriptions, and configuration guides must be written in English.
- **Code Comments**: Must be written in English.
- **Code Identifiers**: Variable names, function names, class names, and file names must be in English.
- **Technical Terminology**: Use standard English technical terms consistently.
- **Memory Content**: Content written to AI memory must be in English, concise, and accurate.

## 2.3. Specific File Type Guidelines
- **Task Reports**:
    - **Path**: `docs/task-reports/`
    - **Naming Convention**: `[task-type]-[YYYY-MM-DD]-[HH-mm-ss].md` (using Beijing time).
    - **Language**: Must be written in Chinese.
- **Test-Related Files**:
    - Test code files are stored in the `test-code/` directory.
    - Test documentation and result reports should be written in Chinese.

## 3. Quality and Execution

### 3.1. Quality Standards
- **Logical Structure**: Documentation should have a clear structure and be easy to read.
- **Consistency**: Maintain consistency in terminology and language style.
- **Clarity**: Avoid ambiguity and use precise technical expressions.

### 3.2. Execution Checklist
Before completing tasks, confirm:
- [ ] All user-facing communication is in Chinese.
- [ ] All technical artifacts (docs, comments) are in English.
- [ ] Code identifiers and technical terms are in English and used consistently.
- [ ] Call MCP feedback_collector -timeout=1200 seconds Report task completion and wait for user feedback on whether the task is completed
