# 手动清理PATH环境变量指南

## 需要手动操作的原因
脚本需要管理员权限来修改系统PATH环境变量。请按照以下步骤手动清理：

## 操作步骤

### 1. 打开系统环境变量设置
1. 按 `Win + R` 键
2. 输入 `sysdm.cpl` 并按回车
3. 点击"环境变量"按钮

### 2. 编辑系统PATH变量
1. 在"系统变量"部分找到"Path"变量
2. 选中"Path"并点击"编辑"
3. 在列表中找到并删除以下条目：
   - `D:\Program Files\nodejs\`
   - `D:\Program Files\nodejs`
   - `C:\nvm4w\nodejs`（如果存在）

### 3. 确保NVM路径存在
确保以下路径在PATH中：
- `C:\Users\<USER>\AppData\Local\nvm`

如果不存在，请添加它。

### 4. 保存更改
1. 点击"确定"保存PATH变量更改
2. 点击"确定"关闭环境变量窗口
3. 点击"确定"关闭系统属性窗口

### 5. 重启命令行工具
关闭所有命令行窗口并重新打开，以使PATH更改生效。

## 验证清理结果
清理完成后，在新的命令行窗口中运行：
```cmd
echo %PATH%
```

确认：
- ✅ 不包含 `D:\Program Files\nodejs`
- ✅ 包含 `C:\Users\<USER>\AppData\Local\nvm`

## 下一步
PATH清理完成后，继续执行：
1. `nvm install 22.14.0`
2. `nvm use 22.14.0`
