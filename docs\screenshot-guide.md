# 截屏工具使用指南

## 功能说明
为Claude Code提供实时截屏功能，便于在代码测试时直接查看运行效果。

## 工具文件清单
按使用环境分类，文件名明确标识适用场景：

### WSL环境专用
- `wsl-screenshot.sh` - WSL bash脚本，调用PowerShell
- `test-code/wsl_screenshot.py` - WSL Python工具，直接调用Windows程序

### Windows环境专用  
- `pwsh-screenshot.ps1` - PowerShell脚本
- `windows-screenshot.bat` - Windows批处理脚本
- `test-code/screenshot_tool.py` - Windows Python工具

## 安装依赖
首次使用需要安装Python依赖：
```bash
pip install Pillow pyautogui
```

## 使用方法

### WSL环境（推荐）
```bash
# 方法1: 使用WSL bash脚本（调用PowerShell）
./wsl-screenshot.sh

# 延迟3秒截屏
./wsl-screenshot.sh 3

# 指定输出文件
./wsl-screenshot.sh 0 "my_test.png"

# 方法2: 直接使用WSL Python工具（纯Python方案）
python3 test-code/wsl_screenshot.py

# 延迟截屏
python3 test-code/wsl_screenshot.py -d 3

# 指定输出文件
python3 test-code/wsl_screenshot.py -o test_result.png
```

### Windows PowerShell环境
```powershell
# PowerShell脚本
./pwsh-screenshot.ps1

# 延迟3秒后截屏
./pwsh-screenshot.ps1 -Delay 3

# 指定保存文件名
./pwsh-screenshot.ps1 -Output "my_test.png"
```

### Windows命令提示符环境
```cmd
# Windows批处理脚本
windows-screenshot.bat

# 延迟5秒后截屏
windows-screenshot.bat 5
```

### 直接调用Python
```bash
# Windows环境
python test-code/screenshot_tool.py

# WSL环境
python3 test-code/wsl_screenshot.py

# 延迟截屏
python3 test-code/wsl_screenshot.py -d 3
```

## 工作流程
1. **运行你的代码**（GUI应用、网页等）
2. **执行截屏命令**：
   - WSL环境：`./wsl-screenshot.sh` 或 `python3 test-code/wsl_screenshot.py`
   - PowerShell环境：`./pwsh-screenshot.ps1`
   - CMD环境：`windows-screenshot.bat`
3. **告诉Claude分析**：直接说"查看Test-Image目录中最新的截屏"或提供具体文件名

## 文件保存位置
- **默认保存位置**：`Test-Image/` 目录（遵循项目结构规则）
- **文件名格式**：`screenshot_YYYYMMDD_HHMMSS.png`
- **目录自动创建**：首次使用时自动创建Test-Image目录
- Claude Code会自动识别并分析Test-Image目录中的截屏内容

## 项目结构组织
根据项目文档管理规则，截屏文件统一保存在Test-Image目录中，保持项目根目录整洁。

## 故障排除
- **依赖缺失**：运行脚本会自动提示安装
- **权限问题**：确保Python有屏幕访问权限
- **WSL环境**：截屏工具需要在Windows环境下运行

## 高级用法
- 结合任务调度器实现定时截屏
- 集成到CI/CD管道进行自动化测试
- 配合OCR工具进行内容识别