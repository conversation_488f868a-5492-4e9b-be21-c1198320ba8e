#!/usr/bin/env python3
import requests
import time

# IP检测
try:
    ip_response = requests.get("http://ipinfo.io", timeout=5)
    if ip_response.status_code == 200:
        ip_data = ip_response.json()
        print(f"🌍 当前IP: {ip_data.get('ip', '未知')} ({ip_data.get('city', '未知')}, {ip_data.get('country', '未知')})")
    else:
        print("🌍 IP检测失败")
except Exception as e:
    print(f"🌍 IP检测错误: {str(e)[:30]}")
print()

# 硬编码配置
API_KEY = "sk-caoq5TSOO8FBxWBotHKaWExnctcoJV8Hbi8AwEWETRCXTQDK"
BASE_URL = "https://anyrouter.top"

def test_endpoint(method, endpoint, headers=None, data=None):
    """测试单个端点"""
    url = f"{BASE_URL}{endpoint}"
    try:
        start_time = time.time()
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=8)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=8)
        else:
            response = requests.request(method, url, headers=headers, json=data, timeout=8)
        
        response_time = round((time.time() - start_time), 1)
        return response.status_code, response_time, response
    except requests.exceptions.Timeout:
        return "超时", ">8.0", None
    except requests.exceptions.ConnectionError:
        return "连接失败", "N/A", None
    except Exception as e:
        return f"错误: {str(e)[:20]}", "N/A", None

def test_server():
    print("🤖 Claude API 多端点状态检测")
    print("=" * 50)
    print(f"📍 服务器: {BASE_URL}")
    print(f"🔑 anyvc101 Key: {API_KEY[:15]}...")
    print()
    
    # 通用请求头
    auth_headers = {
        "Content-Type": "application/json",
        "x-api-key": API_KEY,
        "anthropic-version": "2023-06-01"
    }
    
    basic_headers = {
        "User-Agent": "Claude-Server-Monitor/1.0"
    }
    
    # 测试用的消息数据
    test_message = {
        "model": "claude-3-haiku-********",
        "max_tokens": 10,
        "messages": [{"role": "user", "content": "Hi"}]
    }
    
    # 定义要测试的端点
    endpoints = [
        # 基础端点
        ("GET", "/", "根路径", basic_headers, None),
        ("GET", "/health", "健康检查", basic_headers, None),
        ("GET", "/status", "状态检查", basic_headers, None),
        ("GET", "/ping", "Ping检查", basic_headers, None),
        
        # API信息端点
        ("GET", "/v1", "API根路径", auth_headers, None),
        ("GET", "/v1/models", "模型列表", auth_headers, None),
        ("GET", "/v1/usage", "使用统计", auth_headers, None),
        ("GET", "/v1/limits", "限流信息", auth_headers, None),
        
        # 主要API端点
        ("POST", "/v1/messages", "消息API", auth_headers, test_message),
        ("POST", "/v1/complete", "完成API", auth_headers, {"prompt": "Hi", "max_tokens": 10}),
        ("POST", "/v1/chat/completions", "聊天完成", auth_headers, {
            "model": "claude-3-haiku-********",
            "messages": [{"role": "user", "content": "Hi"}],
            "max_tokens": 10
        }),
        
        # 其他可能的端点
        ("GET", "/v1/account", "账户信息", auth_headers, None),
        ("GET", "/v1/billing", "计费信息", auth_headers, None),
        ("GET", "/v1/keys", "密钥信息", auth_headers, None),
        ("POST", "/v1/embeddings", "嵌入API", auth_headers, {
            "input": "Hello",
            "model": "claude-3-haiku-********"
        }),
    ]
    
    print("📊 端点测试结果:")
    print("-" * 50)
    
    for method, endpoint, name, headers, data in endpoints:
        status, response_time, response = test_endpoint(method, endpoint, headers, data)
        
        # 格式化显示
        status_str = str(status)
        time_str = f"{response_time}秒" if isinstance(response_time, (int, float)) else response_time
        
        # 状态码颜色标识
        if isinstance(status, int):
            if status == 200:
                status_display = f"✅ {status}状态"
            elif 400 <= status < 500:
                status_display = f"⚠️ {status}状态"
            elif status >= 500:
                status_display = f"❌ {status}状态"
            else:
                status_display = f"ℹ️ {status}状态"
        else:
            status_display = f"❌ {status}"
        
        print(f"{name:12} ({endpoint:20}) - {status_display}, {time_str:>8}响应")
        
        # 如果有额外信息可以显示
        if response and response.status_code == 200 and endpoint in ["/v1/models", "/v1/usage"]:
            try:
                data = response.json()
                if endpoint == "/v1/models" and "data" in data:
                    model_count = len(data["data"])
                    print(f"             → 可用模型数量: {model_count}")
                elif endpoint == "/v1/usage":
                    if "usage" in data:
                        print(f"             → 使用情况: {data['usage']}")
            except:
                pass
    
    print()
    print("=" * 50)
    print("💡 说明:")
    print("   ✅ 200状态 = 正常工作")
    print("   ⚠️ 4xx状态 = 客户端错误（权限、参数等）") 
    print("   ❌ 5xx状态 = 服务器错误")
    print("   ❌ 其他 = 网络或连接问题")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    test_server()