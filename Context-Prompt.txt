工具使用（Tool Use）：确保智能体在需要时能够访问外部信息，并为工具设计易于LLM理解的输入参数和输出格式。
短期记忆（Short-term Memory）：在持续的多轮对话中，动态创建对话摘要，并将其作为后续交互的上下文，防止信息丢失。
长期记忆（Long-term Memory）：当用户在过去的对话中表达过特定偏好时，系统能够自动获取这些信息并在新对话中加以利用。
提示工程（Prompt Engineering）：在最终的提示词中，清晰地列出智能体应遵循的行为准则和详细指令。
检索（Retrieval）：在调用LLM之前，根据用户查询动态地从知识库（如向量数据库）中获取相关信息，并将其注入到提示词中。
 优秀的做法是：
  - 初始阶段：核心架构和约束
  - 迭代阶段：相关模块和接口
  - 优化阶段：性能要求和边界条件
认知路径设计：
  - 建立清晰的问题-解决方案映射
  - 提供决策树和约束条件
  - 创建可验证的检查点

  代码质量的本质提升：
  糟糕的上下文 → AI瞎猜 → 低质量代码
  优秀的上下文 → AI精准理解 →
  符合规范的高质量代码

 - 如何让AI理解我的真实意图而不是字面需求？
  - 如何建立可重复、可验证的代码质量标准？
  - 如何设计渐进式的问题解决流程？

  上下文工程的终极目标：让AI成为你思维的延伸，而
  不是盲目的代码生成器。
