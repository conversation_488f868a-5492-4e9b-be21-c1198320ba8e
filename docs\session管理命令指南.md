# Session管理命令指南

## 常见Session管理场景

### 1. Linux/Unix系统Session管理

#### 查看当前session
```bash
# 查看所有登录session
who
w
last

# 查看当前用户session
id
whoami
```

#### 删除/终止session
```bash
# 终止指定用户的session
sudo pkill -u username

# 终止指定终端的session
sudo pkill -t pts/1

# 强制登出用户
sudo skill -KILL -u username
```

### 2. tmux Session管理

#### 查看session
```bash
# 列出所有tmux session
tmux list-sessions
tmux ls

# 查看详细session信息
tmux list-sessions -F "#{session_name}: #{session_windows} windows (created #{session_created_string}) [#{session_width}x#{session_height}]"
```

#### 删除session
```bash
# 删除指定session
tmux kill-session -t session_name

# 删除所有session
tmux kill-server

# 删除除当前session外的所有session
tmux kill-session -a
```

### 3. screen Session管理

#### 查看session
```bash
# 列出所有screen session
screen -ls
screen -list
```

#### 删除session
```bash
# 终止指定session
screen -S session_name -X quit

# 强制终止session
screen -X -S session_name kill
```

### 4. SSH Session管理

#### 查看SSH连接
```bash
# 查看当前SSH连接
ss -tnp | grep :22
netstat -tnp | grep :22

# 查看登录用户
w
who
```

#### 终止SSH session
```bash
# 终止指定SSH连接
sudo kill -HUP PID

# 断开指定用户的SSH连接
sudo pkill -f "sshd: username"
```

### 5. 数据库Session管理

#### MySQL
```sql
-- 查看当前连接
SHOW PROCESSLIST;

-- 终止指定连接
KILL CONNECTION_ID;
```

#### PostgreSQL
```sql
-- 查看当前连接
SELECT * FROM pg_stat_activity;

-- 终止指定连接
SELECT pg_terminate_backend(pid);
```

### 6. Web应用Session管理

#### 浏览器Session
- 清除浏览器cookies和session storage
- 使用开发者工具清除特定domain的session

#### 应用程序Session
- 查看应用程序文档中的session管理命令
- 通常有专门的管理界面或API

## 通用Session清理步骤

### 步骤1: 识别Session类型
确定你要管理的是哪种类型的session：
- 系统登录session
- 应用程序session
- 数据库连接session
- 远程连接session

### 步骤2: 查看Session列表
使用相应的命令查看当前所有session

### 步骤3: 识别无效Session
检查以下特征来识别无效session：
- 长时间无活动
- 已断开的连接
- 重复的连接
- 异常状态的session

### 步骤4: 安全删除
- 确认session确实无效
- 使用适当的命令删除
- 验证删除结果

## 安全注意事项

⚠️ **重要提醒**:
- 删除session前确认不会影响正在进行的工作
- 避免删除当前正在使用的session
- 对于生产环境，建议先备份或记录session信息
- 某些session可能需要管理员权限才能删除

## 故障排除

### 如果无法删除session
1. 检查权限是否足够
2. 确认session ID或名称正确
3. 尝试使用强制删除命令
4. 重启相关服务（最后手段）

### 如果误删重要session
1. 立即重新建立连接
2. 检查是否有自动恢复机制
3. 从备份中恢复数据（如果有）