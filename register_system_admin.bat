@echo off
echo Registering Chrome MCP Bridge at system level...
echo This requires administrator privileges.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges.
    echo.
) else (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM Execute system-level registration
echo Executing system-level registration...
node "C:\Users\<USER>\AppData\Roaming\npm\node_modules\mcp-chrome-bridge\dist\cli.js" register --system

if %errorLevel% == 0 (
    echo.
    echo ✓ System-level registration completed successfully!
    echo.
    echo The Chrome MCP Bridge is now permanently registered at system level.
    echo This registration will persist across user sessions and system restarts.
) else (
    echo.
    echo ✗ System-level registration failed.
    echo Error code: %errorLevel%
    echo.
    echo Please check the error messages above for details.
)

echo.
pause
