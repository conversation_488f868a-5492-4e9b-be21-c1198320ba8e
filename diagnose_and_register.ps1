# Comprehensive diagnosis and registration script
Write-Host "Chrome MCP Bridge Diagnosis and System Registration" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host ""

# Check administrator privileges
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
Write-Host "Administrator privileges: $isAdmin" -ForegroundColor $(if($isAdmin){"Green"}else{"Red"})

# Check Node.js installation
try {
    $nodeVersion = & node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js not found!" -ForegroundColor Red
    exit 1
}

# Check mcp-chrome-bridge installation
$bridgePath = "C:\Users\<USER>\AppData\Roaming\npm\node_modules\mcp-chrome-bridge"
$cliPath = "$bridgePath\dist\cli.js"

Write-Host "Bridge installation path: $bridgePath" -ForegroundColor Yellow
Write-Host "CLI script exists: $(Test-Path $cliPath)" -ForegroundColor $(if(Test-Path $cliPath){"Green"}else{"Red"})

# Check current registrations
Write-Host ""
Write-Host "Current Registration Status:" -ForegroundColor Cyan
Write-Host "----------------------------" -ForegroundColor Cyan

# User-level registration
$userManifest = "$env:APPDATA\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost.json"
$userExists = Test-Path $userManifest
Write-Host "User-level manifest: $userExists" -ForegroundColor $(if($userExists){"Green"}else{"Red"})

if ($userExists) {
    Write-Host "User manifest path: $userManifest" -ForegroundColor Gray
}

# System-level registration
$systemManifest = "${env:ProgramFiles}\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost.json"
$systemExists = Test-Path $systemManifest
Write-Host "System-level manifest: $systemExists" -ForegroundColor $(if($systemExists){"Green"}else{"Red"})

if ($systemExists) {
    Write-Host "System manifest path: $systemManifest" -ForegroundColor Gray
}

# Check registry entries
Write-Host ""
Write-Host "Registry Entries:" -ForegroundColor Cyan
Write-Host "-----------------" -ForegroundColor Cyan

try {
    $userReg = Get-ItemProperty -Path "HKCU:\Software\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost" -ErrorAction SilentlyContinue
    Write-Host "User registry entry: $($userReg -ne $null)" -ForegroundColor $(if($userReg){"Green"}else{"Red"})
} catch {
    Write-Host "User registry entry: False" -ForegroundColor Red
}

try {
    $systemReg = Get-ItemProperty -Path "HKLM:\Software\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost" -ErrorAction SilentlyContinue
    Write-Host "System registry entry: $($systemReg -ne $null)" -ForegroundColor $(if($systemReg){"Green"}else{"Red"})
} catch {
    Write-Host "System registry entry: False" -ForegroundColor Red
}

# If running as admin, attempt system registration
if ($isAdmin) {
    Write-Host ""
    Write-Host "Attempting System-Level Registration..." -ForegroundColor Yellow
    Write-Host "=======================================" -ForegroundColor Yellow
    
    try {
        # Create system directory if it doesn't exist
        $systemDir = "${env:ProgramFiles}\Google\Chrome\NativeMessagingHosts"
        if (-not (Test-Path $systemDir)) {
            Write-Host "Creating system directory: $systemDir" -ForegroundColor Yellow
            New-Item -ItemType Directory -Path $systemDir -Force | Out-Null
        }
        
        # Create manifest content
        $manifestContent = @{
            name = "com.chromemcp.nativehost"
            description = "Node.js Host for Browser Bridge Extension"
            path = "$bridgePath\dist\run_host.bat"
            type = "stdio"
            allowed_origins = @("chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/")
        }
        
        # Write manifest file
        $manifestJson = $manifestContent | ConvertTo-Json -Depth 10
        $systemManifestPath = "$systemDir\com.chromemcp.nativehost.json"
        
        Write-Host "Writing system manifest to: $systemManifestPath" -ForegroundColor Yellow
        $manifestJson | Out-File -FilePath $systemManifestPath -Encoding UTF8
        
        # Create system registry entry
        Write-Host "Creating system registry entry..." -ForegroundColor Yellow
        $regPath = "HKLM:\Software\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost"
        
        if (-not (Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "(Default)" -Value $systemManifestPath
        
        Write-Host ""
        Write-Host "✓ System-level registration completed!" -ForegroundColor Green
        Write-Host "✓ Manifest file created: $systemManifestPath" -ForegroundColor Green
        Write-Host "✓ Registry entry created: $regPath" -ForegroundColor Green
        
    } catch {
        Write-Host ""
        Write-Host "✗ System registration failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "⚠ System-level registration requires administrator privileges" -ForegroundColor Yellow
    Write-Host "  Please run this script as administrator to complete system registration" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Diagnosis completed." -ForegroundColor Cyan
