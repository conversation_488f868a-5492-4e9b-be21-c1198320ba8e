# PowerShell script to run system-level registration as administrator
param(
    [switch]$Force
)

Write-Host "Chrome MCP Bridge System-Level Registration" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Check if already running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Host "✓ Running with administrator privileges" -ForegroundColor Green
    Write-Host ""
    
    # Execute the registration directly
    Write-Host "Executing system-level registration..." -ForegroundColor Yellow
    
    try {
        $result = & node "C:\Users\<USER>\AppData\Roaming\npm\node_modules\mcp-chrome-bridge\dist\cli.js" register --system
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✓ System-level registration completed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "The Chrome MCP Bridge is now permanently registered at system level." -ForegroundColor Green
            Write-Host "This registration will persist across user sessions and system restarts." -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "✗ System-level registration failed." -ForegroundColor Red
            Write-Host "Error code: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host ""
        Write-Host "✗ Error during registration: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠ This script requires administrator privileges" -ForegroundColor Yellow
    Write-Host ""
    
    if (-not $Force) {
        $response = Read-Host "Do you want to restart this script as administrator? (y/n)"
        if ($response -ne 'y' -and $response -ne 'Y') {
            Write-Host "Operation cancelled." -ForegroundColor Yellow
            exit 1
        }
    }
    
    Write-Host "Restarting as administrator..." -ForegroundColor Yellow
    
    try {
        # Get the current script path
        $scriptPath = $MyInvocation.MyCommand.Path
        
        # Start new PowerShell process as administrator
        Start-Process PowerShell -ArgumentList "-ExecutionPolicy Bypass -File `"$scriptPath`" -Force" -Verb RunAs -Wait
        
        Write-Host "Administrator session completed." -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to start administrator session: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
