#!/usr/bin/env python3
"""
Windows截屏工具 - 专用于Windows环境
文件名: screenshot_tool.py
环境要求: Windows系统，Python环境
功能: 使用PIL和pyautogui进行屏幕截图
"""

import os
import sys
import time
from datetime import datetime
try:
    from PIL import Image, ImageGrab
    import pyautogui
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False

def take_screenshot(save_path=None, delay=0):
    """
    截取当前屏幕
    Args:
        save_path: 保存路径，默认保存到G:\screenshot目录
        delay: 延迟秒数，用于准备截屏
    """
    if not DEPENDENCIES_AVAILABLE:
        print("依赖库未安装，正在尝试安装...")
        install_dependencies()
        return
    
    if delay > 0:
        print(f"等待 {delay} 秒后截屏...")
        time.sleep(delay)
    
    # 生成文件名并确保保存到G:\screenshot目录
    if not save_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = f"screenshot_{timestamp}.png"
    
    # 确保保存到G:\screenshot目录
    if not os.path.isabs(save_path):
        test_image_dir = "G:\\screenshot"
        os.makedirs(test_image_dir, exist_ok=True)  # 确保目录存在
        save_path = os.path.join(test_image_dir, save_path)
    
    try:
        # 方法1: 使用PIL
        screenshot = ImageGrab.grab()
        screenshot.save(save_path)
        print(f"截屏已保存到: {save_path}")
        return save_path
    except Exception as e:
        print(f"PIL截屏失败: {e}")
        
        try:
            # 方法2: 使用pyautogui
            screenshot = pyautogui.screenshot()
            screenshot.save(save_path)
            print(f"截屏已保存到: {save_path}")
            return save_path
        except Exception as e2:
            print(f"pyautogui截屏也失败: {e2}")
            return None

def install_dependencies():
    """安装必要的依赖"""
    print("安装截屏依赖库...")
    os.system("pip install Pillow pyautogui")
    print("依赖安装完成，请重新运行脚本")

def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description='截屏工具')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-d', '--delay', type=int, default=0, help='延迟秒数')
    
    args = parser.parse_args()
    
    result = take_screenshot(args.output, args.delay)
    if result:
        print(f"✅ 截屏成功: {os.path.abspath(result)}")
    else:
        print("❌ 截屏失败")

if __name__ == "__main__":
    main()