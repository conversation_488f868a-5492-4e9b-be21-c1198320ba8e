# 项目文件组织标准

## 📋 文件分类标准

### 🎯 核心功能文件
**位置**: 项目根目录
**特征**: 可直接运行的主要功能脚本
**示例**:
- `*.py` - Python主脚本
- `*.pyw` - Python无控制台脚本
- `*.js` - JavaScript主文件
- `*.exe` - 可执行文件

### 🧪 测试文件
**位置**: `test-code/` 目录
**特征**: 配置、测试、构建相关脚本
**示例**:
- `*.bat` - Windows批处理脚本
- `*.ps1` - PowerShell脚本
- `*test*` - 测试相关文件
- `*setup*` - 配置脚本
- `*.zip` - 测试包

### 📚 文档文件
**位置**: `docs/` 和 `test-doc/` 目录及子目录
**特征**: 说明文档、使用指南、帮助文件、任务报告
**组织规则**:
- `docs/task-reports/` - **任务完成报告**（格式：任务名称-完成.md）
- `docs/` - 项目指南和标准文档
- `test-doc/` - 测试相关文档，按功能模块创建子目录
- `test-doc/python-gui/` - Python GUI相关文档
- `test-doc/powershell/` - PowerShell相关文档
- `test-doc/general/` - 通用测试文档

### ⚙️ 配置文件
**位置**: 根目录或相应配置目录
**特征**: 环境设置、项目配置
**示例**:
- `.gitignore` - Git配置
- `package.json` - Node.js配置
- `requirements.txt` - Python依赖

### 🗑️ 临时文件
**位置**: 应及时清理
**特征**: 可删除的测试产物、缓存文件
**处理**: 定期清理或添加到.gitignore

## 🏗️ 目录结构规范

```
项目根目录/
├── 核心功能文件 (.py, .pyw, .js等)
├── 配置文件 (.gitignore, package.json等)
├── README.md (项目说明)
│
├── test-code/ (测试和配置脚本)
│   ├── *.bat (批处理脚本)
│   ├── *.ps1 (PowerShell脚本)
│   ├── *test* (测试文件)
│   └── *.zip (测试包)
│
├── test-doc/ (文档目录)
│   ├── 功能模块1/
│   ├── 功能模块2/
│   └── general/ (通用文档)
│
├── docs/ (正式文档)
│   ├── 指南文档
│   └── task-reports/ (任务报告)
│
└── assets/ (资源文件)
    ├── images/
    └── data/
```

## 📝 命名约定

### 文件命名
- **核心功能**: 功能描述_版本.扩展名
- **测试文件**: test-功能描述.扩展名
- **配置文件**: setup-功能描述.扩展名
- **文档文件**: 功能描述-说明.md
- **任务报告**: 任务名称-完成.md（存放在docs/task-reports/）

### 目录命名
- 使用小写字母和连字符
- 功能模块目录: `module-name/`
- 测试目录: `test-code/`
- 文档目录: `test-doc/`

## 🔄 文件迁移规则

### 自动分类规则
1. **检查文件扩展名和内容**
2. **识别文件功能类型**
3. **按分类标准移动到相应目录**
4. **更新相关引用和路径**

### 迁移优先级
1. 核心功能文件 → 根目录
2. 测试配置文件 → test-code/
3. 文档说明文件 → test-doc/相应子目录
4. 临时文件 → 清理或归档

## ✅ 组织检查清单

- [ ] 核心功能文件在根目录
- [ ] 测试文件在test-code/目录
- [ ] 文档文件按模块组织在test-doc/
- [ ] **任务完成报告在docs/task-reports/目录**
- [ ] 项目根目录有README.md
- [ ] 目录结构清晰易懂
- [ ] 文件命名规范一致
- [ ] 临时文件已清理

## 🎯 最佳实践

1. **保持根目录简洁** - 只放核心功能文件
2. **按功能分组** - 相关文件放在同一目录
3. **文档就近原则** - 文档放在相关功能附近
4. **定期整理** - 及时清理临时文件
5. **更新README** - 保持项目说明最新

## 🔧 工具支持

- 自动化脚本检测文件类型
- 批量文件移动和重命名
- 目录结构验证工具
- 文档生成和更新工具

---

**注意**: 这些标准应在每个新项目开始时应用，并在项目发展过程中持续维护。
