# Google Gemini CLI 命令无法识别问题解决指南

## 🔍 问题诊断结果

### ✅ 已确认的状态
- ✅ `@google/gemini-cli@0.1.13` 已正确安装
- ✅ 可执行文件已创建：`C:\Users\<USER>\AppData\Roaming\npm\gemini.cmd`
- ✅ 命令名称确实是 `gemini`
- ✅ NVM环境配置正常

### ❌ 问题根源
**npm全局bin目录不在系统PATH环境变量中**

缺失路径：`C:\Users\<USER>\AppData\Roaming\npm`

## 🛠️ 解决方案

### 方案一：手动添加PATH（推荐）

#### 步骤1：打开系统环境变量设置
1. 按 `Win + R` 键
2. 输入 `sysdm.cpl` 并按回车
3. 点击"环境变量"按钮

#### 步骤2：编辑系统PATH变量
1. 在"系统变量"部分找到"Path"变量
2. 选中"Path"并点击"编辑"
3. 点击"新建"
4. 添加路径：`C:\Users\<USER>\AppData\Roaming\npm`
5. 点击"确定"保存所有更改

#### 步骤3：重启命令行
1. 关闭所有PowerShell/命令行窗口
2. 重新打开PowerShell
3. 测试命令：`gemini --version`

### 方案二：临时解决方案（立即可用）

在当前PowerShell会话中临时添加PATH：

```powershell
# 临时添加到当前会话的PATH
$env:PATH = "C:\Users\<USER>\AppData\Roaming\npm;" + $env:PATH

# 测试命令
gemini --version
```

**注意**: 这种方法只在当前会话有效，关闭窗口后需要重新执行。

### 方案三：使用完整路径（临时）

直接使用完整路径调用命令：

```powershell
# 使用完整路径
& "C:\Users\<USER>\AppData\Roaming\npm\gemini.cmd" --version

# 或者创建别名
Set-Alias gemini "C:\Users\<USER>\AppData\Roaming\npm\gemini.cmd"
gemini --version
```

## 🎯 验证解决结果

完成PATH配置后，验证以下命令：

```powershell
# 检查命令是否可识别
where gemini

# 检查版本
gemini --version

# 查看帮助
gemini --help
```

预期结果：
```
PS> where gemini
C:\Users\<USER>\AppData\Roaming\npm\gemini.cmd

PS> gemini --version
0.1.13
```

## 📚 Gemini CLI 使用指南

### 基本命令
```powershell
# 查看版本
gemini --version

# 查看帮助
gemini --help

# 配置API密钥
gemini config set api-key YOUR_API_KEY

# 发送聊天消息
gemini chat "Hello, how are you?"

# 生成内容
gemini generate "Write a poem about coding"
```

### 配置文件
Gemini CLI 的配置通常保存在：
- Windows: `%USERPROFILE%\.gemini\config`
- 或项目目录的 `.gemini` 文件

## 🔧 其他全局npm包

修复PATH后，以下全局包也将可用：
- `browser-tools-server` (AgentDesk AI浏览器工具)
- `mcp-server-filesystem` (模型上下文协议文件系统服务器)
- `npm`, `npx` (Node包管理器)
- `update` (更新检查工具)

## ⚠️ 注意事项

1. **管理员权限**: 修改系统PATH需要管理员权限
2. **重启必需**: 修改PATH后必须重启命令行工具
3. **NVM兼容**: 此配置与NVM完全兼容
4. **安全性**: 确保只添加可信任的目录到PATH

## 🎉 完成后的好处

- ✅ 所有npm全局包命令都可直接使用
- ✅ 无需记住复杂的完整路径
- ✅ 与NVM环境完美集成
- ✅ 支持命令自动补全
