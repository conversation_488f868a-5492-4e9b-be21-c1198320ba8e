# PowerShell启动目录智能配置指南

## 问题描述

PowerShell每次启动时都会自动跳转到 `D:\XunneiDownload-D` 目录，而不是期望的项目工作目录。传统的硬编码路径解决方案会在切换项目时造成新的问题。

## 智能解决方案概述

本指南提供了一个智能的PowerShell Profile配置，能够：
- 自动检测当前工作环境（VSCode、项目目录等）
- 根据项目特征智能选择启动目录
- 避免硬编码路径，适应不同项目需求
- 提供优雅的降级策略

## 问题诊断

### 根本原因
在PowerShell Profile文件 `C:\Users\<USER>\Documents\WindowsPowerShell\profile.ps1` 中存在以下配置：
```powershell
Set-Location -Path D:\XunneiDownload-D
```

### 诊断过程
1. **检查当前工作目录状态**：
   - PowerShell工作目录：`D:\XunneiDownload-D`
   - .NET当前目录：`G:\z-vscode-claudecode`
   - 说明PowerShell Profile在启动时修改了工作目录

2. **检查Profile文件**：
   - 主Profile：`$PROFILE.CurrentUserCurrentHost`
   - 通用Profile：`$PROFILE.CurrentUserAllHosts` ← 问题所在
   - 发现通用Profile中有硬编码的目录切换命令

3. **检查VSCode配置**：
   - `.vscode/settings.json` 中 `"terminal.integrated.cwd": null` 配置正确
   - VSCode本身没有问题

## 智能解决方案

### 方案特点
1. **智能检测**：自动识别VSCode工作区、项目目录标识符
2. **优先级策略**：按优先级选择最合适的启动目录
3. **无硬编码**：避免固定路径，适应不同项目
4. **优雅降级**：提供多层备选方案

### 检测优先级
1. **VSCode工作区**：检测VSCode环境变量，使用工作区目录
2. **项目标识符**：检测 `.git`、`.vscode`、`package.json` 等项目文件
3. **父目录搜索**：向上搜索3级目录寻找项目标识符
4. **预设目录**：使用预定义的项目目录列表
5. **用户目录**：最后降级到用户主目录

### 实施步骤

#### 1. 备份原始配置
```powershell
Copy-Item $PROFILE.CurrentUserAllHosts "$($PROFILE.CurrentUserAllHosts).backup"
```

#### 2. 应用智能Profile
使用提供的 `smart_profile.ps1` 文件：
```powershell
Copy-Item "smart_profile.ps1" $PROFILE.CurrentUserAllHosts -Force
```

#### 3. 重新加载配置
```powershell
. $PROFILE.CurrentUserAllHosts
```

### 智能Profile核心功能

```powershell
# 智能目录检测函数
Function Set-SmartWorkingDirectory {
    # 优先级1: VSCode工作区检测
    if ($env:VSCODE_PID -or $env:TERM_PROGRAM -eq "vscode") {
        $vscodeWorkspace = $env:VSCODE_CWD
        if ($vscodeWorkspace -and (Test-Path $vscodeWorkspace)) {
            Set-Location $vscodeWorkspace
            return
        }
    }

    # 优先级2: 项目标识符检测
    $projectIndicators = @(".git", ".vscode", "package.json", "requirements.txt")
    # ... 检测逻辑

    # 优先级3: 父目录搜索
    # 优先级4: 预设目录
    # 优先级5: 用户目录降级
}
```

## 验证结果

### 测试方法
1. **当前会话测试**：重新加载Profile后检查工作目录
2. **新会话测试**：启动新的PowerShell会话验证默认目录
3. **批处理测试**：使用测试脚本验证配置

### 测试结果
- ✅ PowerShell启动时显示：`PowerShell started in project directory: G:\z-vscode-claudecode`
- ✅ 工作目录正确设置为：`G:\z-vscode-claudecode`
- ✅ 新的PowerShell会话自动进入项目目录
- ✅ VSCode终端集成正常工作

## 配置文件位置

### PowerShell Profile文件层次
- `$PROFILE.AllUsersAllHosts` - 所有用户，所有主机
- `$PROFILE.AllUsersCurrentHost` - 所有用户，当前主机
- `$PROFILE.CurrentUserAllHosts` - 当前用户，所有主机 ← **修改的文件**
- `$PROFILE.CurrentUserCurrentHost` - 当前用户，当前主机

### 实际文件路径
- 通用Profile：`C:\Users\<USER>\Documents\WindowsPowerShell\profile.ps1`
- 特定Profile：`C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1`

## 注意事项

1. **编码问题**：确保Profile文件使用UTF-8编码，避免中文字符乱码
2. **权限问题**：修改Profile文件需要适当的文件系统权限
3. **执行策略**：确保PowerShell执行策略允许运行Profile脚本
4. **备份重要**：修改前务必备份原始配置文件

## 故障排除

### 如果配置不生效
1. 检查PowerShell执行策略：`Get-ExecutionPolicy`
2. 手动重新加载Profile：`. $PROFILE.CurrentUserAllHosts`
3. 重启VSCode或PowerShell会话
4. 检查文件权限和路径是否正确

### 恢复原始配置
```powershell
Copy-Item "$($PROFILE.CurrentUserAllHosts).backup" $PROFILE.CurrentUserAllHosts -Force
```

## 总结

通过修改PowerShell Profile文件，成功解决了启动目录问题：
- **问题根源**：Profile文件中的硬编码目录切换
- **解决方法**：更新Profile文件，设置正确的默认目录
- **效果验证**：PowerShell现在默认启动在项目工作目录
- **用户体验**：无需每次手动切换目录，减少操作失误
