# Node.js 环境配置备份

**备份时间**: 2025-07-20 19:00:00  
**目的**: 在切换到NVM管理之前备份当前配置

## 当前环境状态

### Node.js 信息
- **版本**: v22.14.0
- **安装路径**: D:\Program Files\nodejs\node.exe
- **类型**: 独立安装

### NPM 信息
- **版本**: 11.4.2
- **安装路径**: D:\Program Files\nodejs\npm
- **全局包路径**: C:\Users\<USER>\AppData\Roaming\npm
- **缓存路径**: C:\Users\<USER>\AppData\Local\npm-cache
- **注册表**: https://registry.npmjs.org/

### NVM 信息
- **版本**: 1.2.2
- **安装路径**: C:\Users\<USER>\AppData\Local\nvm
- **状态**: 已安装但未管理任何版本

### PATH 环境变量（完整）
```
C:\Program Files\PowerShell\7-preview\;
c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;
C:\Program Files\Microsoft MPI\Bin\;
D:\Python-v39\Scripts\;
D:\Python-v39\;
C:\WINDOWS\system32;
C:\WINDOWS;
C:\WINDOWS\System32\Wbem;
C:\WINDOWS\System32\WindowsPowerShell\v1.0\;
C:\Program Files\dotnet\;
C:\Program Files (x86)\GtkSharp\2.12\bin;
C:\WINDOWS\System32\OpenSSH\;
C:\Program Files\Microsoft\Web Platform Installer\;
E:\gcloud\bin;
D:\Program Files\PuTTY\;
D:\ffmpeg\bin;
C:\Program Files (x86)\dotnet\;
D:\Programs\Python\Python311\Scripts\;
D:\Programs\Python\Python311\;
C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
E:\;
C:\ProgramData\chocolatey\bin;
D:\Program Files\Git\cmd;
c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;
C:\WINDOWS\system32;
C:\WINDOWS;
C:\WINDOWS\System32\Wbem;
C:\WINDOWS\System32\WindowsPowerShell\v1.0\;
C:\WINDOWS\System32\OpenSSH\;
C:\Program Files\Cloudflare\Cloudflare WARP\;
G:\rovocli\;
C:\Users\<USER>\AppData\Local\nvm;
C:\nvm4w\nodejs;
C:\Program Files\PowerShell\7-preview\;
c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;
C:\Program Files\Microsoft MPI\Bin\;
D:\Python-v39\Scripts\;
D:\Python-v39\;
C:\WINDOWS\system32;
C:\WINDOWS;
C:\WINDOWS\System32\Wbem;
C:\WINDOWS\System32\WindowsPowerShell\v1.0\;
C:\Program Files\dotnet\;
C:\Program Files (x86)\GtkSharp\2.12\bin;
C:\WINDOWS\System32\OpenSSH\;
C:\Program Files\Microsoft\Web Platform Installer\;
E:\gcloud\bin;
D:\Program Files\PuTTY\;
D:\ffmpeg\bin;
C:\Program Files (x86)\dotnet\;
D:\Programs\Python\Python311\Scripts\;
D:\Programs\Python\Python311\;
C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
E:\;
D:\Program Files\nodejs\;
C:\ProgramData\chocolatey\bin;
D:\Program Files\Git\cmd;
c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;
C:\WINDOWS\system32;
C:\WINDOWS;
C:\WINDOWS\System32\Wbem;
C:\WINDOWS\System32\WindowsPowerShell\v1.0\;
C:\WINDOWS\System32\OpenSSH\;
C:\Program Files\Cloudflare\Cloudflare WARP\;
G:\rovocli\;
C:\Program Files\PowerShell\7-preview\;
```

### 需要移除的路径
- `D:\Program Files\nodejs\;`
- `C:\nvm4w\nodejs;` (如果存在但为空)

### 需要保留的路径
- `C:\Users\<USER>\AppData\Local\nvm;`

## 恢复说明

如果需要恢复到当前配置：
1. 重新安装 Node.js v22.14.0 到 D:\Program Files\nodejs\
2. 将 `D:\Program Files\nodejs\` 添加回 PATH 环境变量
3. 恢复 npm 全局配置

## 备份完成

✅ 环境配置已备份，可以安全进行 NVM 配置
