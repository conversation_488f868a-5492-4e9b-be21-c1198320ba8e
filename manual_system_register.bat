@echo off
echo Chrome MCP Bridge System Registration
echo =====================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Running with administrator privileges
    echo.
) else (
    echo ✗ This script requires administrator privileges
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Creating system-level Native Messaging Host registration...
echo.

REM Create system directory
set SYSTEM_DIR=%ProgramFiles%\Google\Chrome\NativeMessagingHosts
echo Creating directory: %SYSTEM_DIR%
if not exist "%SYSTEM_DIR%" (
    mkdir "%SYSTEM_DIR%"
    if %errorLevel% == 0 (
        echo ✓ Directory created successfully
    ) else (
        echo ✗ Failed to create directory
        pause
        exit /b 1
    )
) else (
    echo ✓ Directory already exists
)

REM Create manifest file
set MANIFEST_FILE=%SYSTEM_DIR%\com.chromemcp.nativehost.json
echo.
echo Creating manifest file: %MANIFEST_FILE%

(
echo {
echo   "name": "com.chromemcp.nativehost",
echo   "description": "Node.js Host for Browser Bridge Extension",
echo   "path": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\run_host.bat",
echo   "type": "stdio",
echo   "allowed_origins": [
echo     "chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/"
echo   ]
echo }
) > "%MANIFEST_FILE%"

if %errorLevel% == 0 (
    echo ✓ Manifest file created successfully
) else (
    echo ✗ Failed to create manifest file
    pause
    exit /b 1
)

REM Create registry entry
echo.
echo Creating system registry entry...
reg add "HKLM\Software\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost" /ve /t REG_SZ /d "%MANIFEST_FILE%" /f

if %errorLevel% == 0 (
    echo ✓ Registry entry created successfully
) else (
    echo ✗ Failed to create registry entry
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ System-level registration completed!
echo ========================================
echo.
echo The Chrome MCP Bridge is now permanently registered at system level.
echo This registration will persist across:
echo - User sessions
echo - System restarts  
echo - User account changes
echo.
echo You can now use the chrome-mcp tools without manual registration.
echo.
pause
